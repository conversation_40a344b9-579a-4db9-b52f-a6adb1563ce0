import { apiClient } from '@/lib/apiClient'
import type { ChatRequest, ChatResponse, ChatMessage, ConversationSummary } from '@/types/chat'

// Chat API endpoints
export const chatApi = {
  /**
   * Send a chat message to the AI assistant
   */
  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await apiClient.post<ChatResponse>('/api/v1/chat/', request)
    return response.data
  },

  /**
   * Get chat health status
   */
  getHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await apiClient.get('/api/v1/chat/health')
    return response.data
  },
}

// Chat History API endpoints (will interact with Su<PERSON><PERSON> directly in hooks)
export const chatHistoryApi = {
  /**
   * Get user's conversation summaries
   */
  getConversations: async (): Promise<ConversationSummary[]> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
    return []
  },

  /**
   * Get full conversation by ID
   */
  getConversation: async (_conversationId: string): Promise<ChatMessage[]> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
    return []
  },

  /**
   * Delete a conversation
   */
  deleteConversation: async (_conversationId: string): Promise<void> => {
    // This will be implemented in the useChatHistory hook using Supabase client
    // Placeholder for now
  },
}

// Health check for the entire backend
export const healthApi = {
  /**
   * Check backend health
   */
  checkHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await apiClient.get('/health')
    return response.data
  },
}

// Export all APIs
export const api = {
  chat: chatApi,
  chatHistory: chatHistoryApi,
  health: healthApi,
}

// Export types
export type { ChatRequest, ChatResponse }
