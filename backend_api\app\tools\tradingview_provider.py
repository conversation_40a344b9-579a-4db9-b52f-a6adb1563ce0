"""
TradingView data provider for TradingIA Backend.

This module provides financial data and technical analysis tools
using the tvDatafeed library. It acts as an abstraction layer
that can be easily replaced with other data providers if needed.
"""

try:
    from tvDatafeed import TvDatafeed, Interval
except ImportError:
    # Mock for testing when tvDatafeed is not available
    class TvDatafeed:
        def get_hist(self, symbol, exchange, interval, n_bars):
            return None

    class Interval:
        in_1_minute = "1m"
        in_5_minute = "5m"
        in_15_minute = "15m"
        in_30_minute = "30m"
        in_1_hour = "1h"
        in_4_hour = "4h"
        in_daily = "1D"
        in_weekly = "1W"
        in_monthly = "1M"
from fastapi import HTTPException, status
from typing import Dict, Any, Optional
import pandas as pd
import logging
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Initialize TvDatafeed client
tv = TvDatafeed()

# Interval mapping
INTERVAL_MAP = {
    "1m": Interval.in_1_minute,
    "5m": Interval.in_5_minute,
    "15m": Interval.in_15_minute,
    "30m": Interval.in_30_minute,
    "1h": Interval.in_1_hour,
    "4h": Interval.in_4_hour,
    "1D": Interval.in_daily,
    "1W": Interval.in_weekly,
    "1M": Interval.in_monthly
}


def get_price_data(symbol: str, interval: str, n_bars: int) -> Dict[str, Any]:
    """
    Obtain historical OHLCV price data for any asset.
    
    This function uses tvDatafeed to retrieve historical price data
    and formats it in a clean, consistent structure for the AI model.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA", "BINANCE:BTCUSDT")
        interval (str): Time interval ("1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M")
        n_bars (int): Number of bars to retrieve (max 5000)
        
    Returns:
        Dict[str, Any]: Formatted price data with OHLCV information
        
    Raises:
        HTTPException: If data retrieval fails or symbol not found
    """
    try:
        # Validate inputs
        if interval not in INTERVAL_MAP:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid interval. Supported intervals: {list(INTERVAL_MAP.keys())}"
            )
        
        if n_bars <= 0 or n_bars > 5000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="n_bars must be between 1 and 5000"
            )
        
        # Parse symbol (format: "EXCHANGE:SYMBOL")
        if ":" in symbol:
            exchange, ticker = symbol.split(":", 1)
        else:
            # Default to NASDAQ if no exchange specified
            exchange = "NASDAQ"
            ticker = symbol
        
        logger.info(f"Fetching price data for {exchange}:{ticker}, interval: {interval}, bars: {n_bars}")
        
        # Get data from TradingView
        data = tv.get_hist(
            symbol=ticker,
            exchange=exchange,
            interval=INTERVAL_MAP[interval],
            n_bars=n_bars
        )
        
        if data is None or data.empty:
            logger.warning(f"No data found for symbol: {exchange}:{ticker}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol: {exchange}:{ticker}"
            )
        
        # Format the data
        formatted_data = {
            "symbol": f"{exchange}:{ticker}",
            "interval": interval,
            "bars_count": len(data),
            "data": []
        }
        
        # Convert DataFrame to list of dictionaries
        for index, row in data.iterrows():
            bar_data = {
                "datetime": index.isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": int(row['volume']) if pd.notna(row['volume']) else 0
            }
            formatted_data["data"].append(bar_data)
        
        # Add summary statistics
        latest_bar = formatted_data["data"][-1] if formatted_data["data"] else None
        if latest_bar:
            formatted_data["latest_price"] = latest_bar["close"]
            formatted_data["latest_datetime"] = latest_bar["datetime"]
        
        logger.info(f"Successfully retrieved {len(formatted_data['data'])} bars for {exchange}:{ticker}")
        return formatted_data
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error fetching price data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch price data: {str(e)}"
        )


def apply_indicator(
    symbol: str,
    interval: str,
    indicator_name: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Calculate and return technical indicator values.

    This function first obtains price data and then applies the specified
    technical indicator calculation over the data.

    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        interval (str): Time interval for the data
        indicator_name (str): Name of the indicator (e.g., "RSI", "MACD", "SMA")
        parameters (Dict[str, Any]): Indicator parameters (e.g., {"length": 14})

    Returns:
        Dict[str, Any]: Indicator values in structured format

    Raises:
        HTTPException: If calculation fails or indicator not supported
    """
    try:
        # Import pandas_ta for technical analysis
        import pandas_ta as ta

        # Supported indicators
        SUPPORTED_INDICATORS = {
            "RSI": "rsi",
            "MACD": "macd",
            "SMA": "sma",
            "EMA": "ema",
            "BBANDS": "bbands",
            "STOCH": "stoch",
            "ATR": "atr",
            "ADX": "adx"
        }

        if indicator_name.upper() not in SUPPORTED_INDICATORS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported indicator. Supported indicators: {list(SUPPORTED_INDICATORS.keys())}"
            )

        # Get price data (default to 100 bars for indicator calculation)
        n_bars = parameters.get("n_bars", 100)
        price_data = get_price_data(symbol, interval, n_bars)

        # Convert to DataFrame for pandas_ta
        df_data = []
        for bar in price_data["data"]:
            df_data.append({
                "open": bar["open"],
                "high": bar["high"],
                "low": bar["low"],
                "close": bar["close"],
                "volume": bar["volume"]
            })

        df = pd.DataFrame(df_data)

        # Apply the indicator
        indicator_func = SUPPORTED_INDICATORS[indicator_name.upper()]

        if indicator_name.upper() == "RSI":
            length = parameters.get("length", 14)
            result = ta.rsi(df["close"], length=length)

        elif indicator_name.upper() == "MACD":
            fast = parameters.get("fast", 12)
            slow = parameters.get("slow", 26)
            signal = parameters.get("signal", 9)
            result = ta.macd(df["close"], fast=fast, slow=slow, signal=signal)

        elif indicator_name.upper() == "SMA":
            length = parameters.get("length", 20)
            result = ta.sma(df["close"], length=length)

        elif indicator_name.upper() == "EMA":
            length = parameters.get("length", 20)
            result = ta.ema(df["close"], length=length)

        elif indicator_name.upper() == "BBANDS":
            length = parameters.get("length", 20)
            std = parameters.get("std", 2)
            result = ta.bbands(df["close"], length=length, std=std)

        elif indicator_name.upper() == "STOCH":
            k = parameters.get("k", 14)
            d = parameters.get("d", 3)
            result = ta.stoch(df["high"], df["low"], df["close"], k=k, d=d)

        elif indicator_name.upper() == "ATR":
            length = parameters.get("length", 14)
            result = ta.atr(df["high"], df["low"], df["close"], length=length)

        elif indicator_name.upper() == "ADX":
            length = parameters.get("length", 14)
            result = ta.adx(df["high"], df["low"], df["close"], length=length)

        # Format the result
        formatted_result = {
            "symbol": symbol,
            "interval": interval,
            "indicator": indicator_name.upper(),
            "parameters": parameters,
            "values": []
        }

        # Handle different result types
        if isinstance(result, pd.Series):
            # Single series (RSI, SMA, EMA, ATR)
            for i, value in enumerate(result):
                if pd.notna(value):
                    formatted_result["values"].append({
                        "index": i,
                        "datetime": price_data["data"][i]["datetime"],
                        "value": float(value)
                    })

        elif isinstance(result, pd.DataFrame):
            # Multiple series (MACD, BBANDS, STOCH, ADX)
            for i in range(len(result)):
                row_data = {"index": i, "datetime": price_data["data"][i]["datetime"]}
                for col in result.columns:
                    if pd.notna(result.iloc[i][col]):
                        row_data[col.lower()] = float(result.iloc[i][col])
                if len(row_data) > 2:  # More than just index and datetime
                    formatted_result["values"].append(row_data)

        # Add latest value summary
        if formatted_result["values"]:
            latest = formatted_result["values"][-1]
            formatted_result["latest_value"] = latest

        logger.info(f"Successfully calculated {indicator_name} for {symbol}")
        return formatted_result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except ImportError:
        logger.error("pandas_ta library not available")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Technical analysis library not available"
        )
    except Exception as e:
        logger.error(f"Error calculating indicator: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate indicator: {str(e)}"
        )


# Tool definitions for Vertex AI
TRADING_TOOLS = [
    {
        "name": "get_price_data",
        "description": "Obtiene datos históricos de precios OHLCV para cualquier activo financiero",
        "parameters": {
            "type": "object",
            "properties": {
                "symbol": {
                    "type": "string",
                    "description": "Símbolo del activo (ej: 'NASDAQ:TSLA', 'BINANCE:BTCUSDT')"
                },
                "interval": {
                    "type": "string",
                    "description": "Intervalo de tiempo",
                    "enum": ["1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M"]
                },
                "n_bars": {
                    "type": "integer",
                    "description": "Número de barras a obtener (máximo 5000)",
                    "minimum": 1,
                    "maximum": 5000
                }
            },
            "required": ["symbol", "interval", "n_bars"]
        }
    },
    {
        "name": "apply_indicator",
        "description": "Calcula indicadores técnicos sobre datos de precio",
        "parameters": {
            "type": "object",
            "properties": {
                "symbol": {
                    "type": "string",
                    "description": "Símbolo del activo"
                },
                "interval": {
                    "type": "string",
                    "description": "Intervalo de tiempo",
                    "enum": ["1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M"]
                },
                "indicator_name": {
                    "type": "string",
                    "description": "Nombre del indicador",
                    "enum": ["RSI", "MACD", "SMA", "EMA", "BBANDS", "STOCH", "ATR", "ADX"]
                },
                "parameters": {
                    "type": "object",
                    "description": "Parámetros del indicador (ej: {'length': 14} para RSI)",
                    "properties": {
                        "length": {"type": "integer", "minimum": 1},
                        "fast": {"type": "integer", "minimum": 1},
                        "slow": {"type": "integer", "minimum": 1},
                        "signal": {"type": "integer", "minimum": 1},
                        "std": {"type": "number", "minimum": 0},
                        "k": {"type": "integer", "minimum": 1},
                        "d": {"type": "integer", "minimum": 1},
                        "n_bars": {"type": "integer", "minimum": 1, "maximum": 5000}
                    }
                }
            },
            "required": ["symbol", "interval", "indicator_name", "parameters"]
        }
    }
]


def get_available_tools():
    """Return the list of available trading tools for Vertex AI."""
    return TRADING_TOOLS
