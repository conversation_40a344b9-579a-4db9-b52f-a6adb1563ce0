"""
Configuration settings for the TradingIA Backend API.

This module centralizes all configuration settings using Pydantic Settings
for type validation and environment variable management.
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application settings
    environment: str = "development"
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    cors_origins: str = "http://localhost:3000,http://localhost:5173"
    
    # Supabase settings
    supabase_url: str
    supabase_key: str
    supabase_service_key: str
    
    # Google Cloud Vertex AI settings
    vertex_ai_project: str
    vertex_ai_location: str = "us-central1"
    google_application_credentials: str = ""
    
    # Stripe settings
    stripe_secret_key: str
    stripe_webhook_secret: str
    
    # Security settings
    jwt_secret_key: str = "your-secret-key-change-in-production"
    jwt_algorithm: str = "HS256"
    
    # Redis settings (for future caching)
    redis_url: str = "redis://localhost:6379"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Convert CORS origins string to list."""
        return [origin.strip() for origin in self.cors_origins.split(",")]


# Global settings instance
settings = Settings()
