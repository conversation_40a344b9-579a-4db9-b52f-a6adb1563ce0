"""
Pytest configuration and shared fixtures.

This module provides common fixtures and configuration
for all tests in the TradingIA backend test suite.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock
from fastapi.testclient import TestClient
import pandas as pd
from datetime import datetime, timedelta
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.main import app


@pytest.fixture(scope="session")
def test_app():
    """Create a test FastAPI application."""
    return app


@pytest.fixture
def client(test_app):
    """Create a test client for the FastAPI app."""
    return TestClient(test_app)


@pytest.fixture
def mock_user():
    """Mock user data for authentication tests."""
    return {
        "id": "user_123456789abcdef",
        "email": "<EMAIL>",
        "email_confirmed_at": "2025-01-01T00:00:00Z",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "user_metadata": {
            "full_name": "Test User",
            "subscription_tier": "free"
        },
        "app_metadata": {
            "provider": "email",
            "providers": ["email"]
        }
    }


@pytest.fixture
def mock_premium_user():
    """Mock premium user data."""
    return {
        "id": "premium_user_123456789",
        "email": "<EMAIL>",
        "email_confirmed_at": "2025-01-01T00:00:00Z",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "user_metadata": {
            "full_name": "Premium User",
            "subscription_tier": "premium"
        },
        "app_metadata": {
            "provider": "email",
            "providers": ["email"]
        }
    }


@pytest.fixture
def valid_jwt_token():
    """Mock valid JWT token."""
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.test_signature"


@pytest.fixture
def auth_headers(valid_jwt_token):
    """Authentication headers with valid token."""
    return {"Authorization": f"Bearer {valid_jwt_token}"}


@pytest.fixture
def sample_price_data():
    """Sample price data for testing."""
    dates = pd.date_range('2025-01-01', periods=30, freq='D')
    return pd.DataFrame({
        'open': [100.0 + i for i in range(30)],
        'high': [105.0 + i for i in range(30)],
        'low': [99.0 + i for i in range(30)],
        'close': [104.0 + i for i in range(30)],
        'volume': [1000 + i * 100 for i in range(30)]
    }, index=dates)


@pytest.fixture
def formatted_price_data():
    """Formatted price data as returned by get_price_data."""
    return {
        "symbol": "NASDAQ:TSLA",
        "interval": "1D",
        "bars_count": 3,
        "latest_price": 248.50,
        "latest_datetime": "2025-01-03T00:00:00",
        "data": [
            {
                "datetime": "2025-01-01T00:00:00",
                "open": 245.0,
                "high": 250.0,
                "low": 244.0,
                "close": 246.0,
                "volume": 1000000
            },
            {
                "datetime": "2025-01-02T00:00:00",
                "open": 246.0,
                "high": 251.0,
                "low": 245.0,
                "close": 247.25,
                "volume": 1100000
            },
            {
                "datetime": "2025-01-03T00:00:00",
                "open": 247.25,
                "high": 252.0,
                "low": 246.0,
                "close": 248.50,
                "volume": 1200000
            }
        ]
    }


@pytest.fixture
def sample_rsi_data():
    """Sample RSI indicator data."""
    return {
        "symbol": "NASDAQ:TSLA",
        "interval": "1D",
        "indicator": "RSI",
        "parameters": {"length": 14},
        "latest_value": {
            "index": 19,
            "datetime": "2025-01-20T00:00:00",
            "value": 65.2
        },
        "values": [
            {
                "index": i,
                "datetime": f"2025-01-{i+1:02d}T00:00:00",
                "value": 50.0 + i
            } for i in range(20)
        ]
    }


@pytest.fixture
def valid_chat_request():
    """Valid chat request payload."""
    return {
        "history": [
            {
                "role": "user",
                "content": "Hola, ¿puedes ayudarme?",
                "timestamp": "2025-01-01T10:00:00Z"
            },
            {
                "role": "assistant",
                "content": "¡Por supuesto! Soy tu asistente financiero. ¿En qué puedo ayudarte?",
                "timestamp": "2025-01-01T10:00:05Z"
            }
        ],
        "message": "¿Cuál es el precio actual de Tesla?"
    }


@pytest.fixture
def empty_chat_request():
    """Chat request with no history."""
    return {
        "history": [],
        "message": "Hola, soy nuevo aquí"
    }


@pytest.fixture
def mock_gemini_model():
    """Mock Gemini model for testing."""
    model = MagicMock()
    model.generate_content = MagicMock()
    return model


@pytest.fixture
def mock_supabase_client():
    """Mock Supabase client for testing."""
    client = MagicMock()
    client.auth.get_user = MagicMock()
    client.table = MagicMock()
    return client


@pytest.fixture
def mock_tv_datafeed():
    """Mock TvDatafeed for testing."""
    tv = MagicMock()
    tv.get_hist = MagicMock()
    return tv


# Async fixtures
@pytest.fixture
async def async_mock_save_history():
    """Async mock for save_chat_history."""
    mock = AsyncMock()
    mock.return_value = {
        "id": "chat_history_123",
        "created_at": "2025-01-01T10:00:00Z",
        "conversation_id": "conv_12345678"
    }
    return mock


@pytest.fixture
async def async_mock_validate_token():
    """Async mock for validate_user_token."""
    mock = AsyncMock()
    return mock


# Test data generators
@pytest.fixture
def generate_ohlcv_data():
    """Factory function to generate OHLCV data."""
    def _generate(symbol="NASDAQ:TSLA", days=30, start_price=100.0):
        dates = pd.date_range('2025-01-01', periods=days, freq='D')
        data = []
        current_price = start_price
        
        for i, date in enumerate(dates):
            # Simple random walk
            change = (i % 3 - 1) * 2  # -2, 0, or 2
            open_price = current_price
            high_price = open_price + abs(change) + 2
            low_price = open_price - abs(change) - 1
            close_price = open_price + change
            volume = 1000000 + i * 10000
            
            data.append({
                "datetime": date.isoformat(),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": volume
            })
            
            current_price = close_price
        
        return {
            "symbol": symbol,
            "interval": "1D",
            "bars_count": len(data),
            "latest_price": data[-1]["close"],
            "latest_datetime": data[-1]["datetime"],
            "data": data
        }
    
    return _generate


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Test environment setup
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Set up test environment variables."""
    test_env_vars = {
        "SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_KEY": "test_anon_key",
        "SUPABASE_SERVICE_KEY": "test_service_key",
        "VERTEX_AI_PROJECT": "test-project",
        "VERTEX_AI_LOCATION": "us-central1",
        "STRIPE_SECRET_KEY": "sk_test_123",
        "STRIPE_WEBHOOK_SECRET": "whsec_test_123",
        "JWT_SECRET_KEY": "test_jwt_secret",
        "ENVIRONMENT": "test"
    }
    
    for key, value in test_env_vars.items():
        monkeypatch.setenv(key, value)
