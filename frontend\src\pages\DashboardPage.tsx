import React from 'react'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { ChatHistoryPanel } from '@/components/dashboard/ChatHistoryPanel'
import { ChatWindow } from '@/components/chat/ChatWindow'
import { MainChart } from '@/components/dashboard/MainChart'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { useChartData } from '@/hooks/useChartData'

export const DashboardPage: React.FC = () => {
  const { chartData } = useChartData()

  const handleConversationSelect = (conversationId: string) => {
    console.log('Selected conversation:', conversationId)
  }

  return (
    <DashboardLayout
      sidebar={
        <ChatHistoryPanel
          onConversationSelect={handleConversationSelect}
          className="h-full border-0"
        />
      }
    >
      <div className="flex h-full">
        {/* Main chat area */}
        <div className="flex-1 p-6">
          <ChatWindow className="h-full" />
        </div>

        {/* Right sidebar - Charts and additional info */}
        <div className="w-80 border-l bg-card p-6 space-y-6">
          {/* Charts */}
          <MainChart
            data={chartData || undefined}
            height={300}
          />

          {/* Market info placeholder */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información del Mercado</CardTitle>
              <CardDescription>
                Datos en tiempo real
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">S&P 500</span>
                  <span className="text-sm font-mono">--</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">NASDAQ</span>
                  <span className="text-sm font-mono">--</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Bitcoin</span>
                  <span className="text-sm font-mono">--</span>
                </div>
                <div className="text-center pt-4">
                  <p className="text-xs text-muted-foreground">
                    Pregunta a la IA por datos específicos
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
