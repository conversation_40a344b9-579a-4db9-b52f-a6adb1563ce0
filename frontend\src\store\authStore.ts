import { create } from 'zustand'
import { supabase } from '@/lib/supabaseClient'
import type { AuthStore, User, Session, LoginFormData, RegisterFormData } from '@/types/auth'

// Create the auth store WITHOUT persistence (temporary fix)
export const useAuthStore = create<AuthStore>()((set, _get) => ({
      // State
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,

      // Actions
      signUp: async (data: RegisterFormData) => {
        set({ isLoading: true })
        
        try {
          const { data: authData, error } = await supabase.auth.signUp({
            email: data.email,
            password: data.password,
            options: {
              data: {
                full_name: data.fullName,
              },
            },
          })

          if (error) {
            set({ isLoading: false })
            return { user: null, error: error.message }
          }

          if (authData.user) {
            set({
              user: authData.user as User,
              session: authData.session,
              isAuthenticated: !!authData.session,
              isLoading: false,
            })
          }

          return { user: authData.user as User, error: null }
        } catch (error) {
          set({ isLoading: false })
          return { 
            user: null, 
            error: error instanceof Error ? error.message : 'An unexpected error occurred' 
          }
        }
      },

      signIn: async (data: LoginFormData) => {
        set({ isLoading: true })
        
        try {
          const { data: authData, error } = await supabase.auth.signInWithPassword({
            email: data.email,
            password: data.password,
          })

          if (error) {
            set({ isLoading: false })
            return { user: null, error: error.message }
          }

          set({
            user: authData.user as User,
            session: authData.session,
            isAuthenticated: !!authData.session,
            isLoading: false,
          })

          return { user: authData.user as User, error: null }
        } catch (error) {
          set({ isLoading: false })
          return { 
            user: null, 
            error: error instanceof Error ? error.message : 'An unexpected error occurred' 
          }
        }
      },

      signOut: async () => {
        set({ isLoading: true })
        
        try {
          const { error } = await supabase.auth.signOut()
          
          if (error) {
            set({ isLoading: false })
            return { error: error.message }
          }

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
          })

          return { error: null }
        } catch (error) {
          set({ isLoading: false })
          return { 
            error: error instanceof Error ? error.message : 'An unexpected error occurred' 
          }
        }
      },

      resetPassword: async (email: string) => {
        set({ isLoading: true })
        
        try {
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`,
          })

          set({ isLoading: false })

          if (error) {
            return { error: error.message }
          }

          return { error: null }
        } catch (error) {
          set({ isLoading: false })
          return { 
            error: error instanceof Error ? error.message : 'An unexpected error occurred' 
          }
        }
      },

      updatePassword: async (password: string) => {
        set({ isLoading: true })
        
        try {
          const { error } = await supabase.auth.updateUser({ password })

          set({ isLoading: false })

          if (error) {
            return { error: error.message }
          }

          return { error: null }
        } catch (error) {
          set({ isLoading: false })
          return { 
            error: error instanceof Error ? error.message : 'An unexpected error occurred' 
          }
        }
      },

      setUser: (user: User | null) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        })
      },

      setSession: (session: Session | null) => {
        set({ 
          session, 
          isAuthenticated: !!session 
        })
      },

      setLoading: (isLoading: boolean) => {
        console.log('📊 Store setLoading:', isLoading)
        set({ isLoading })
      },

      clearAuth: () => {
        console.log('🧹 Store clearAuth called')
        set({
          user: null,
          session: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },
    }))
