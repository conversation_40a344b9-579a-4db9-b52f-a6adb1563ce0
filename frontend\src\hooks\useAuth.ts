import { useEffect } from 'react'
import { useAuthStore } from '@/store/authStore'
import { supabase } from '@/lib/supabaseClient'
import type { User } from '@/types/auth'

/**
 * Custom hook for authentication management
 * Provides auth state and actions, handles session persistence
 */
export const useAuth = () => {
  const {
    user,
    session,
    isLoading,
    isAuthenticated,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    setUser,
    setSession,
    setLoading,
    clearAuth,
  } = useAuthStore()

  // Set up auth state listener on mount
  useEffect(() => {
    let mounted = true

    // Get initial session
    const getInitialSession = async () => {
      try {
        setLoading(true)
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          clearAuth()
          return
        }

        if (mounted) {
          if (session) {
            setUser(session.user as User)
            setSession(session)
          } else {
            clearAuth()
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        if (mounted) {
          clearAuth()
        }
      } finally {
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        console.log('Auth state changed:', event, session?.user?.email)

        switch (event) {
          case 'SIGNED_IN':
            if (session) {
              setUser(session.user as User)
              setSession(session)
            }
            break
          
          case 'SIGNED_OUT':
            clearAuth()
            break
          
          case 'TOKEN_REFRESHED':
            if (session) {
              setUser(session.user as User)
              setSession(session)
            }
            break
          
          case 'USER_UPDATED':
            if (session) {
              setUser(session.user as User)
              setSession(session)
            }
            break
          
          default:
            break
        }
        
        setLoading(false)
      }
    )

    // Cleanup function
    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [setUser, setSession, setLoading, clearAuth])

  // Enhanced sign out with cleanup
  const handleSignOut = async () => {
    try {
      setLoading(true)
      const result = await signOut()
      
      // Clear any additional app state here if needed
      // For example, clear chat history, user preferences, etc.
      
      return result
    } catch (error) {
      console.error('Error during sign out:', error)
      return { error: 'Error al cerrar sesión' }
    } finally {
      setLoading(false)
    }
  }

  // Check if user has specific permissions/roles
  const hasRole = (role: string): boolean => {
    return user?.user_metadata?.role === role
  }

  const hasPermission = (permission: string): boolean => {
    const userPermissions = user?.user_metadata?.permissions || []
    return userPermissions.includes(permission)
  }

  // Get user display name
  const getDisplayName = (): string => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
    }
    if (user?.email) {
      return user.email.split('@')[0]
    }
    return 'Usuario'
  }

  // Get user avatar URL
  const getAvatarUrl = (): string | null => {
    return user?.user_metadata?.avatar_url || null
  }

  // Check if session is expired
  const isSessionExpired = (): boolean => {
    if (!session) return true
    
    const expiresAt = session.expires_at
    if (!expiresAt) return false
    
    return Date.now() / 1000 > expiresAt
  }

  // Refresh session manually
  const refreshSession = async () => {
    try {
      setLoading(true)
      const { data: { session }, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        clearAuth()
        return { error: error.message }
      }

      if (session) {
        setUser(session.user as User)
        setSession(session)
      }

      return { error: null }
    } catch (error) {
      console.error('Error in refreshSession:', error)
      clearAuth()
      return { error: 'Error al actualizar la sesión' }
    } finally {
      setLoading(false)
    }
  }

  return {
    // State
    user,
    session,
    isLoading,
    isAuthenticated,
    
    // Actions
    signUp,
    signIn,
    signOut: handleSignOut,
    resetPassword,
    updatePassword,
    refreshSession,
    
    // Utility functions
    hasRole,
    hasPermission,
    getDisplayName,
    getAvatarUrl,
    isSessionExpired,
  }
}
